<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">

	<meta name="viewport" content="width=device-width, initial-scale=1.0">

	<title>Social Media Buttons With Tooltip Hover Effect</title>

	<!-- font-awesome 5 CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />

	<!-- Custom CSS -->
	<style type="text/css" media="all">
*{
  font-family:'Arial', san serif;
}
html, body{
  display:grid;
  height:100%;
  width: 100%;
  place-items:center;
  background: 
     linear-gradient(315deg, 
                    #ffffff, 
                    #d7e1ec );
}
.wrapper{
  display:inline-flex;
}
.wrapper .icon{
  margin: 0 20px;
  cursor:pointer;
  display:flex;
  align-items:center;
  justify-content:center;
  flex-direction:column;
  position: relative;
  z-index:2;
}
.wrapper .icon span{
  position:relative;
  z-index:2;
  height: 60px;
  width: 60px;
  display:block;
  background: #fff;
  box-shadow: 0 10px 10px rgba(0,0,0,0.1);
  border-radius:50%;
  text-align:center;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
.wrapper .icon span i{
  font-size:25px;
  line-height:60px;
}
.wrapper .icon .tooltip{
  position:absolute;
  top:0px;
  background: #fff;
  box-shadow: 0 10px 10px rgba(0,0,0,0.1);
  font-size: 20px;
  padding: 10px 18px;
  border-radius: 25px;
  color:white;
  opacity:0;
  pointer-events: none;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
.wrapper .icon:hover .tooltip{
  opacity:1;
  pointer-events: auto;
  top:-70px;
}
.wrapper .icon .tooltip:before{
  position:absolute;
  content:"";
  height:15px;
  width:15px;
  bottom:-8px;
  left:50%;
  transform: translateX(-50%) rotate(45deg);
}
.wrapper .icon:hover span,
.wrapp .icon:hover .tooltip{
  text-shadow: 0px -1px 0px rgba(0,0,0,0.4);
}
.wrapper .icon:hover span{
  color: #fff;
}
.wrapper .facebook:hover span,
.wrapper .facebook:hover .tooltip,
.wrapper .facebook:hover .tooltip:before{
  background:#3B5999;
}
.wrapper .twitter:hover span,
.wrapper .twitter:hover .tooltip,
.wrapper .twitter:hover .tooltip:before{
  background:#46C1F6;
}
.wrapper .github:hover span,
.wrapper .github:hover .tooltip,
.wrapper .github:hover .tooltip:before{
  background:#333;
}
.wrapper .youtube:hover span,
.wrapper .youtube:hover .tooltip,
.wrapper .youtube:hover .tooltip:before{
  background:#de463b;
}
	</style>

</head>
<body>
  <a href="https://github.com/Mark-Eugene-Barasu/HTML-and-CSS-tooltip-contact-page">Back to Github</a>
  <div class="wrapper">

    <div class="icon facebook">
      <div class="tooltip">Facebook</div>
      <span><i class="fab fa-facebook-f"></i></span>
    </div>
  
    <div class="icon twitter">
      <div class="tooltip">Twitter</div>
      <span><i class="fab fa-twitter"></i></span>
    </div>

    <div class="icon github">
       <div class="tooltip">github</div>
       <span><i class="fab fa-github"></i></span>
    </div>

	<div class="icon youtube">
	    <div class="tooltip">YouTube</div>
	      <span><i class="fab fa-youtube"></i></span>
	</div>
	
  </div>
  
<!-- Author -->
<!-- https://medium.com/@PurpleGreenLemon -->
<!-- https://medium.com/madhash/how-to-code-pure-css-social-media-buttons-with-tooltip-hover-effect-3524a928398b -->
</body>
</html>