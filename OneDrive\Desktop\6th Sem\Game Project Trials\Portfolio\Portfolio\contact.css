/* ===== CONTACT PAGE STYLES ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation Styles */
nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav_logo img {
    height: 50px;
    border-radius: 50%;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav ul li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: #007bff;
}

.contact_btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact_btn:hover,
.contact_btn.active {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Contact Hero Section */
.contact-hero {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.contact-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.contact-hero p {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Content */
.contact-content {
    padding: 80px 0;
    background: white;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

/* Contact Form Section */
.contact-form-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.contact-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Contact Info Section */
.contact-info-section h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.contact-info-section > p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-methods {
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.contact-details h3 {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    color: #333;
}

.contact-details p {
    color: #666;
    margin: 0;
}

/* Social Links */
.social-links h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.social-icons a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.faq-item h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #007bff;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero h1 {
        font-size: 2.5rem;
    }

    .contact-hero p {
        font-size: 1.1rem;
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .social-icons {
        justify-content: center;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    nav ul {
        display: none;
    }
}

@media (max-width: 480px) {
    .contact-hero {
        padding: 100px 0 40px;
    }

    .contact-hero h1 {
        font-size: 2rem;
    }

    .contact-content {
        padding: 60px 0;
    }

    .contact-form {
        padding: 1rem;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
    }

    .faq-section {
        padding: 60px 0;
    }
}