@font-face {
    font-family: "Media";
    src: url('font\ 1.otf');
}
@font-face {
    font-family: "SF Pro";
    src: url('font\ 2.otf');
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
::-webkit-scrollbar{
  display: none; 
}
::selection{
  background-color: #f78c00;
  color: black;
}

.button-loader{
  width: 100%;
  height: 100vh;
  background-color: transparent;
  position: fixed;
  z-index: 999;
  pointer-events: none;
  display: none;
}
.lft-slide{
  width: 50%;
  height: 100%;
  background-color: #191919;
  position: absolute;
  left: -100%;
}
.rgt-slide{
  width: 50%;
  height: 100%;
  background-color: #191919;
  position: absolute;
  right: -100%;
}




/* <====================================================== Loader Coded Here ======================================================> */


.loader{
  width: 100%;
  height: 100vh;
  background-color: #1B1B1B;
  position: fixed;
  z-index: 999;
}



nav{
    width: 100%;
    height: 100px;
    position: fixed;
    top: 0px;
    background: linear-gradient(135deg, #0d0d0d, #1a1a1a, #000000);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: "SF Pro";
    padding: 0 100px;
    z-index: 90;
}
.nav_logo{
    width: 12%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.nav_logo img{
    width: 100%;
    height: 230%;
    object-fit: cover;
}
nav ul{
    list-style: none;
}
nav ul li{
    display: inline-block;
    margin: 0 35px;
}
nav ul li a{
    text-decoration: none;
    color: rgb(255, 255, 255);
    font-weight: 300;
}

nav a.contact_btn{
    display: inline-block;
    width: 150px;
    height: 50px;
    background-color: rgb(255, 255, 255);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: rgb(0, 0, 0);
    border-radius: 8px;
    transform-origin: center;
    transition: .5s ease;
}
nav a.contact_btn:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.543);
}

.herobox{
    width:100%;
    height: 100vh;
    background-color: rgb(255, 255, 255);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.herobox h1{
    font-family: "Media";
    font-size: 85px;
    text-align: center;
    width:80%;
    margin-top: 20px;
}
.herobox a{
    display: flex;
    align-items: center;
    justify-content: center;
    width:250px;
    height:55px;
    background-color: white;
    border: 2px solid black;
    border-radius: 8px;
    text-decoration: none;
    font-family: "SF Pro";
    color: black;
    margin-top: 30px;
    transform-origin: center;
    transition: .3s ease;
}
.herobox a:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 7px 10px rgba(0, 0, 0, 0.336);
}

.section-1{
    width: 100%;
    height: 160vh;
    background-color:white;
    position: relative;
}

.card-container{
    width:100%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    position: absolute;
    top: 5%;
}

.card-1 img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: .5s ease;
}
.card-1:hover img{
    transform:scale(1.1);
}
.card-1{
    width: 18%;
    height: 80%;
    position: absolute;
    overflow: hidden;
    top: 0%;
    border-radius: 15px;
    cursor: pointer;
}
.card-2 img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: .5s ease;
}
.card-2:hover img{
    transform:scale(1.1);
}
.card-2{
    width: 18%;
    height: 80%;
    position: absolute;
    overflow: hidden;
    left: 20.5%;
    top: 8%;
    border-radius: 15px;
    cursor: pointer;
}
.card-3 img{
    width:100%;
    height: 100%;
    object-fit: cover;
    transition: .5s ease;
}
.card-3:hover img{
    transform:scale(1.1);
}
.card-3{
    width: 18%;
    height: 80%;
    position: absolute;
    overflow: hidden;
    left: 41%;
    bottom: 0%;
    border-radius: 15px;
    cursor: pointer;
}

.card-4 img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: .5s ease;
}
.card-4:hover img{
    transform:scale(1.1);
}
.card-4{
    width: 18%;
    height: 80%;
    position: absolute;
    overflow: hidden;
    top: 0%;
    right: 0%;
    border-radius: 15px;
    cursor: pointer;
}

.card-5 img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: .5s ease;
}
.card-5:hover img{
    transform:scale(1.1); 
}
.card-5{
    width: 18%;
    height: 80%;
    overflow: hidden;
    position: absolute;
    right: 20.5%;
    top: 8%;
    border-radius: 15px;
    cursor: pointer;
}

.section-1 h1{
    position: absolute;
    left:50%;
    transform: translateX(-50%);
    top:60%;
    opacity: 0%;
    font-family: "Media";
    font-size: 52px;
}

.section-1 p.p1{
    position: absolute;
    left:50%;
    transform: translateX(-50%);
    top:68%;
    font-size: 28px;
    text-align: center;
    font-family: "SF Pro";
}

.section-1 p.p2{
    position: absolute;
    left:50%;
    transform: translateX(-50%);
    top:78%;
    font-size: 28px;
    font-family: "SF Pro";
    text-align: center;
}
.section p.p2 strong{
    color: goldenrod;
}


.section-2{
  width: 100%;
  height: 120vh;
  background-color: #1B1B1B;
  position: relative;
}
.section-2 h1{
  position: absolute;
  color: white;
  font-family: "Media";
  font-size: 55px;
  letter-spacing: 1px;
  top: 10%;
  left: 10%;
}
.carousel-wrapper{
  width: 80%;
  height: 62%;
  overflow: hidden;
  position: absolute;
  left: 50%;
  top: 55%;
  transform: translate(-50%,-50%);
  /* border: 2px solid white; */
}
.slides-holder{
  width: 236%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
}
.slide{
  width: 13%;
  height: 100%;
  border-radius: 30px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}
.overlay{
  width: 100%;
  height: 0%;
  background: rgb(0,0,0);
  background: linear-gradient(0deg, rgba(0,0,0,0) -34%, rgba(38,38,38,0.87718837535014) 0%, rgba(17,17,17,0.8715861344537815) 100%);
  position: absolute;
  z-index: 5;
  transition: .5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0%;

}
.overlay a{
  text-decoration: none;
}
.overlay i{
  font-size: 25px;
  margin: 0 15px;
  background-color: white;
  color: #1B1B1B;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: .3s ease;
}
.overlay i:hover{
  background-color: #f78c00;
  color: white;
}
.slide:hover .overlay{
  height: 100%;
  opacity: 100%;
}

.tool-github{
  width: 100px;
  height: 30px;
  background-color: white;
  position: absolute;
  top: 56%;
  border-radius: 50px;
  left: 27%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "SF Pro";
  font-weight: 800;
  letter-spacing: 2px;
  opacity: 0%;
  transition: .5s ease;
}
.tool-livedemo{
  width: 120px;
  height: 30px;
  background-color: white;
  position: absolute;
  top: 56%;
  border-radius: 50px;
  left: 45%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "SF Pro";
  font-weight: 800;
  letter-spacing: 1px;
  opacity: 0%;
  transition: .5s ease;
}
.slide img{
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
  transition: .5s ease;
  position: absolute;
}
.left-button{
  width: 65px;
  height: 65px;
  border-radius: 50%;
  background-color: transparent;
  overflow: hidden;
  border: 2px solid white;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 25px;
  position: absolute;
  left: 2%;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.left-button::before{
  content: "";
  width: 65px;
  height: 65px;
  background-color: white;
  position: absolute;
  border-radius: 50%;
  left: 100%;
  top: -100%;
  transition: .5s ease;
}
.left-button:hover i{
  color: #1B1B1B;
}
.left-button:hover::before{
  left:0%;
  top: 0%;
}
.left-button i{
  pointer-events: none;
  transition: .5s ease;
  z-index: 80;
}

.right-button{
  width: 65px;
  height: 65px;
  border-radius: 50%;
  background-color: transparent;
  overflow: hidden;
  border: 2px solid white;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 25px;
  position: absolute;
  right: 2%;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.right-button::before{
  content: "";
  width: 65px;
  height: 65px;
  background-color: white;
  position: absolute;
  border-radius: 50%;
  left: 100%;
  top: -100%;
  transition: .5s ease;
}
.right-button:hover i{
  color: #1B1B1B;
}
.right-button:hover::before{
  left:0%;
  top: 0%;
}
.right-button i{
  pointer-events: none;
  transition: .5s ease;
  z-index: 80;
}


.section-7{
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  background-color: #1B1B1B;
  position: relative;
}
.section-7 h2{
  position: absolute;
  z-index: 50;
  font-family: "SF Pro";
  font-size: 14px;
  letter-spacing: 10px;
  color: #f78c00;
  left: 50%;
  transform: translateX(-50%);
  top: 35%;
}
.section-7 h1{
  font-family: "Media";
  font-size: 50px;
  z-index: 50;
  position: absolute;
  color: white;
  left: 50%;
  transform: translateX(-50%);
  top: 40%;
}
.section-7 p{
  position: absolute;
  z-index: 50;
  color: white;
  left: 50%;
  transform: translateX(-50%);
  font-family: "SF Pro";
  text-align: center;
  width: 50%;
  font-size: 20px;
  top: 52%;
}
.section-7 a{
  position: absolute;
  z-index: 50;
  left: 50%;
  transform: translateX(-50%);
  text-decoration: none;
  color: white;
  font-family: "SF Pro";
  font-size: 18px;
  width: 200px;
  height: 50px;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 63%;
  transform-origin: center;
  transition: .3s ease;
}
.section-7 a:hover{
  width: 220px;
}
.section-7::after{
  content: "";
  width: 100%;
  height: 100%;
  background-color: #1b1b1bd3;
  position: absolute;
}
.col-section-7{
  width: 24%;
  height: 220vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  top: -87%;
}
.card-1-section-7{
  width: 100%;
  height: 32%;
  border-radius: 20px;
  overflow: hidden;
}
.card-1-section-7 img{
  width: 100%;
  height: 100%;
  object-fit: cover;
}

